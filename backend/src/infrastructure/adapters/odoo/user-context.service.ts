import { Injectable, Logger, Inject, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { UserContext } from './odoo-connection-pool.service';
import { OdooConnectionConfig } from '../../../shared/domain/value-objects/odoo-connection-config';
import { UserOdooMappingService } from './user-odoo-mapping.service';

export interface AuthenticatedUser {
  id: string;
  username: string;
  email?: string;
  sessionId?: string;
  // Add other user properties as needed
}

export interface RequestWithUser extends Request {
  user?: AuthenticatedUser;
  sessionId?: string;
  sessionID?: string; // Express session ID
  session?: any; // Express session object
}

@Injectable({ scope: Scope.REQUEST })
export class UserContextService {
  private readonly logger = new Logger(UserContextService.name);
  private userContext: UserContext | null = null;

  constructor(
    @Inject(REQUEST) private readonly request: RequestWithUser,
    private readonly userOdooMappingService: UserOdooMappingService,
  ) {}

  /**
   * Set user context with Odoo configuration
   * Enhanced to create unique user ID based on Odoo credentials
   * If password is missing, attempts to retrieve from encrypted mapping
   */
  async setUserContext(odooConfig: OdooConnectionConfig): Promise<void> {
    const baseUser = this.extractUserFromRequest();

    // If password is missing (e.g., from JWT), try to retrieve from mapping
    let finalConfig = odooConfig;
    if (!odooConfig.password || odooConfig.password === '') {
      try {
        // Generate instance ID to find the mapping
        const instanceId = this.generateInstanceId(odooConfig);
        const mapping = await this.userOdooMappingService.getUserMapping(baseUser.id, instanceId);
        if (mapping) {
          finalConfig = await this.userOdooMappingService.getDecryptedConfig(mapping);
          this.logger.debug(`Retrieved password from encrypted mapping for user ${baseUser.id}`);
        }
      } catch (error) {
        this.logger.warn(`Could not retrieve password from mapping for user ${baseUser.id}:`, error);
        // Continue with provided config (might work in development)
      }
    }

    // CLEAN APPROACH: Always use Enhanced User ID for consistency
    // This ensures connection pool keys are always the same format
    let userId: string;

    // Check if this is already an enhanced user ID from JWT token
    // Enhanced format: "anon_OjoxOmN1_aHR0cHM6" (base + "_" + odooHash)
    // Base format: "anon_OjoxOmN1" (just base)
    const underscoreCount = (baseUser.id.match(/_/g) || []).length;
    const isEnhancedUserId = underscoreCount >= 2;

    if (isEnhancedUserId) {
      // Already enhanced (from JWT token)
      userId = baseUser.id;
      this.logger.debug(`Using enhanced user ID from JWT: ${userId}`);
    } else {
      // Base user ID - enhance it with Odoo configuration hash
      userId = this.generateUniqueUserId(baseUser.id, finalConfig);
      this.logger.debug(`Generated enhanced user ID: ${userId} (base: ${baseUser.id})`);
    }

    this.userContext = {
      userId: userId,
      sessionId: baseUser.sessionId || this.generateSessionId(),
      odooConfig: finalConfig,
    };

    this.logger.debug(`User context set for user: ${userId}`);
  }

  /**
   * Get current user context
   */
  getUserContext(): UserContext {
    if (!this.userContext) {
      throw new Error('User context not set. Call setUserContext() first.');
    }
    return this.userContext;
  }

  /**
   * Check if user context is set
   */
  hasUserContext(): boolean {
    return this.userContext !== null;
  }

  /**
   * Get user ID from context
   */
  getUserId(): string {
    const context = this.getUserContext();
    return context.userId;
  }

  /**
   * Get session ID from context
   */
  getSessionId(): string {
    const context = this.getUserContext();
    return context.sessionId;
  }

  /**
   * Update Odoo configuration in current context
   */
  updateOdooConfig(odooConfig: OdooConnectionConfig): void {
    if (!this.userContext) {
      throw new Error('User context not set. Call setUserContext() first.');
    }
    
    this.userContext.odooConfig = odooConfig;
    this.logger.debug(`Updated Odoo config for user: ${this.userContext.userId}`);
  }

  /**
   * Extract user information from request
   * Production-ready authentication with multiple strategies
   */
  private extractUserFromRequest(): AuthenticatedUser {
    // Priority 1: From Passport JWT (after guard validation)
    if (this.request.user) {
      const user = this.request.user as any; // Type assertion for Passport user
      this.logger.debug(`Passport JWT authenticated user: ${user.id}`);
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        sessionId: user.sessionId || this.generateSessionId(),
      };
    }

    // Priority 2: Manual JWT Token Authentication (fallback)
    const authHeader = this.request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decoded = this.verifyJWT(token); // Use proper JWT verification

        this.logger.debug(`Manual JWT authenticated user: ${decoded.sub || decoded.userId}`);
        return {
          id: decoded.sub || decoded.userId,
          username: decoded.odooUsername || decoded.username,
          email: decoded.email,
          sessionId: decoded.sessionId || this.generateSessionId(),
        };
      } catch (error) {
        this.logger.error('JWT verification failed', error);
        throw new Error('Invalid or expired JWT token');
      }
    }

    // Priority 2: Passport.js authenticated user object
    if (this.request.user) {
      const user = this.request.user as any; // Type assertion for Passport user
      this.logger.debug(`Passport authenticated user: ${user.id}`);
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        sessionId: user.sessionId || this.request.sessionId || this.generateSessionId(),
      };
    }

    // Priority 3: Express session-based authentication
    if (this.request.session && (this.request.session as any).user) {
      const sessionUser = (this.request.session as any).user;
      this.logger.debug(`Session authenticated user: ${sessionUser.id}`);
      return {
        id: sessionUser.id,
        username: sessionUser.username,
        email: sessionUser.email,
        sessionId: this.request.sessionID || this.request.sessionId,
      };
    }

    // Priority 4: Custom headers (for testing/development)
    const userId = this.request.headers['x-user-id'] as string;
    const username = this.request.headers['x-username'] as string;

    if (userId && username) {
      this.logger.debug(`Header authenticated user: ${userId}`);
      return {
        id: userId,
        username: username,
        sessionId: this.generateSessionId(),
      };
    }

    // Priority 5: Anonymous user (for public endpoints like /connect)
    // Allow anonymous users in both development and production for public endpoints
    const anonymousId = this.generateAnonymousUserId();
    this.logger.warn(`No authenticated user found, using anonymous user: ${anonymousId}`);
    return {
      id: anonymousId,
      username: 'anonymous',
      sessionId: this.generateSessionId(),
    };
  }

  /**
   * Verify JWT token (production-ready with proper validation)
   * TODO: Replace with proper JWT library like jsonwebtoken
   */
  private verifyJWT(token: string): any {
    try {
      // In production, use proper JWT verification:
      // const jwt = require('jsonwebtoken');
      // return jwt.verify(token, process.env.JWT_SECRET);

      // For now, decode and validate basic structure
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString('utf-8'));

      // Basic validation
      if (!payload.sub && !payload.userId) {
        throw new Error('JWT missing user identifier');
      }

      // Check expiration
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        throw new Error('JWT token expired');
      }

      return payload;
    } catch (error) {
      throw new Error(`JWT verification failed: ${error.message}`);
    }
  }

  /**
   * Decode JWT token (legacy method - use verifyJWT instead)
   * @deprecated Use verifyJWT for production
   */
  private decodeJWT(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = Buffer.from(payload, 'base64').toString('utf-8');
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique user ID that includes Odoo credentials for proper isolation
   */
  private generateUniqueUserId(baseUserId: string, odooConfig: OdooConnectionConfig): string {
    // Create unique identifier that includes Odoo connection details
    const odooIdentifier = `${odooConfig.host}:${odooConfig.database}:${odooConfig.username}`;
    const hash = Buffer.from(odooIdentifier).toString('base64').substr(0, 8);
    return `${baseUserId}_${hash}`;
  }

  /**
   * Generate anonymous user ID for development
   * Uses deterministic approach based on IP and User-Agent for consistent session management
   */
  private generateAnonymousUserId(): string {
    const ip = this.request.ip || this.request.connection.remoteAddress || 'unknown';
    const userAgent = this.request.headers['user-agent'] || 'unknown';

    // Use only IP and User-Agent for deterministic user ID generation
    // This ensures the same user gets the same ID across requests
    const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substr(0, 8);
    return `anon_${hash}`;
  }

  /**
   * Generate unique instance ID for Odoo configuration (matches UserOdooMappingService)
   */
  private generateInstanceId(config: OdooConnectionConfig): string {
    const hash = Buffer.from(`${config.host}:${config.database}:${config.username}`)
      .toString('base64')
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 12);
    return `odoo_${hash}`;
  }

  /**
   * Get request metadata for logging/debugging
   */
  getRequestMetadata() {
    return {
      ip: this.request.ip,
      userAgent: this.request.headers['user-agent'],
      method: this.request.method,
      url: this.request.url,
      timestamp: new Date().toISOString(),
    };
  }
}
