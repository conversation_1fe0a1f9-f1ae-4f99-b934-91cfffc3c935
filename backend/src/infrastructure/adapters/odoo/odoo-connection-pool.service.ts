import { Injectable, Lo<PERSON>, On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { LRUCache } from 'lru-cache';
import { UniversalOdooAdapter } from './universal-odoo-adapter';
import { OdooConnectionConfig } from '../../../shared/domain/value-objects/odoo-connection-config';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';
import { PerformanceMonitorService } from './performance-monitor.service';

export interface UserContext {
  userId: string;
  sessionId: string;
  odooConfig: OdooConnectionConfig;
}

export interface CachedConnection {
  adapter: UniversalOdooAdapter;
  lastUsed: Date;
  connectionConfig: OdooConnectionConfig;
  createdAt: Date;
  usageCount: number;
  lastHealthCheck: Date;
  isHealthy: boolean;
  consecutiveFailures: number;
}

export interface PoolMetrics {
  size: number;
  maxSize: number;
  utilizationPercent: number;
  averageAgeMinutes: number;
  oldestConnectionMinutes: number;
  newestConnectionMinutes: number;
  totalUsageCount: number;
  averageUsageCount: number;
  healthyConnections: number;
  unhealthyConnections: number;
  lastCleanupTime: Date | null;
  lastHealthCheckTime: Date | null;
}

export interface HealthCheckResult {
  key: string;
  healthy: boolean;
  lastUsed: Date;
  lastHealthCheck: Date;
  consecutiveFailures: number;
  responseTime?: number;
  error?: string;
}

@Injectable()
export class OdooConnectionPoolService implements OnModuleDestroy, OnModuleInit {
  private readonly logger = new Logger(OdooConnectionPoolService.name);

  // Enhanced pool configuration
  private readonly poolConfig = {
    maxConnections: parseInt(process.env.ODOO_POOL_MAX_SIZE || '100'),
    ttlMinutes: parseInt(process.env.ODOO_POOL_TTL_MINUTES || '30'),
    healthCheckIntervalMinutes: parseInt(process.env.ODOO_POOL_HEALTH_CHECK_INTERVAL || '5'),
    cleanupIntervalMinutes: parseInt(process.env.ODOO_POOL_CLEANUP_INTERVAL || '10'),
    maxConsecutiveFailures: parseInt(process.env.ODOO_POOL_MAX_FAILURES || '3'),
    connectionTimeoutMs: parseInt(process.env.ODOO_CONNECTION_TIMEOUT_MS || '30000'),
  };

  // Timers for background tasks
  private healthCheckTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;
  private lastCleanupTime: Date | null = null;
  private lastHealthCheckTime: Date | null = null;
  
  private readonly connectionPool = new LRUCache<string, CachedConnection>({
    max: this.poolConfig.maxConnections,
    ttl: this.poolConfig.ttlMinutes * 60 * 1000,
    dispose: async (cachedConnection, key) => {
      try {
        await this.disposeConnection(cachedConnection, key);
      } catch (error) {
        this.logger.error(`Error disposing connection for key: ${key}`, error);
      }
    },
    updateAgeOnGet: true, // Reset TTL when accessed
    updateAgeOnHas: false,
  });

  constructor(
    private readonly xmlRpcProtocol: XmlRpcProtocol,
    private readonly jsonRpcProtocol: JsonRpcProtocol,
    private readonly restApiProtocol: RestApiProtocol,
    private readonly odooV18Adapter: OdooV18Adapter,
    private readonly odooV17Adapter: OdooV17Adapter,
    private readonly odooV15Adapter: OdooV15Adapter,
    private readonly odooV13Adapter: OdooV13Adapter,
    private readonly performanceMonitor: PerformanceMonitorService,
  ) {}

  /**
   * Initialize background tasks when module starts
   */
  async onModuleInit() {
    this.logger.log('Initializing Odoo Connection Pool...');
    this.logger.log(`Pool Configuration: max=${this.poolConfig.maxConnections}, ttl=${this.poolConfig.ttlMinutes}min`);

    // Start background health checks
    this.startHealthCheckTimer();

    // Start background cleanup
    this.startCleanupTimer();

    this.logger.log('Odoo Connection Pool initialized successfully');
  }

  /**
   * Get or create an Odoo connection for a user
   */
  async getConnection(userContext: UserContext): Promise<UniversalOdooAdapter> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(userContext);

    this.logger.debug(`Looking for connection with cache key: ${cacheKey}`);
    this.logger.debug(`Current pool keys: ${Array.from(this.connectionPool.keys()).join(', ')}`);

    try {
      // Check if connection exists and is valid
      const cachedConnection = this.connectionPool.get(cacheKey);
      if (cachedConnection) {
        this.logger.debug(`Found cached connection for user: ${userContext.userId}, validating...`);

        // Quick health check if connection hasn't been checked recently
        const needsHealthCheck = this.needsHealthCheck(cachedConnection);
        if (needsHealthCheck) {
          const isValid = await this.validateConnectionWithTimeout(cachedConnection.adapter);
          cachedConnection.isHealthy = isValid;
          cachedConnection.lastHealthCheck = new Date();

          if (!isValid) {
            cachedConnection.consecutiveFailures++;
            this.logger.warn(
              `Connection validation failed for user: ${userContext.userId} (failures: ${cachedConnection.consecutiveFailures})`
            );

            // Remove connection if it has too many failures
            if (cachedConnection.consecutiveFailures >= this.poolConfig.maxConsecutiveFailures) {
              this.logger.warn(`Removing unhealthy connection for user: ${userContext.userId}`);
              this.connectionPool.delete(cacheKey);
            } else {
              // Try to reconnect
              try {
                await cachedConnection.adapter.connect();
                await cachedConnection.adapter.authenticate();
                cachedConnection.isHealthy = true;
                cachedConnection.consecutiveFailures = 0;
              } catch (reconnectError) {
                this.logger.warn(`Failed to reconnect for user: ${userContext.userId}`, reconnectError);
                this.connectionPool.delete(cacheKey);
              }
            }
          } else {
            // Reset failure count on successful validation
            cachedConnection.consecutiveFailures = 0;
          }
        }

        // Use connection if it's healthy
        if (cachedConnection.isHealthy) {
          cachedConnection.lastUsed = new Date();
          cachedConnection.usageCount++;

          const duration = Date.now() - startTime;
          this.performanceMonitor.recordMetric({
            operation: 'getConnection',
            duration,
            timestamp: new Date(),
            success: true,
          });

          this.logger.debug(`Reusing valid connection for user: ${userContext.userId}`);
          return cachedConnection.adapter;
        }
      }

      // Create new connection
      this.logger.log(`Creating new connection for user: ${userContext.userId}`);
      const adapter = await this.createConnectionWithRetry(userContext.odooConfig);

      // Cache the connection with enhanced metadata
      const now = new Date();
      const newCachedConnection: CachedConnection = {
        adapter,
        lastUsed: now,
        connectionConfig: userContext.odooConfig,
        createdAt: now,
        usageCount: 1,
        lastHealthCheck: now,
        isHealthy: true,
        consecutiveFailures: 0,
      };

      this.connectionPool.set(cacheKey, newCachedConnection);

      const duration = Date.now() - startTime;
      this.performanceMonitor.recordMetric({
        operation: 'getConnection',
        duration,
        timestamp: new Date(),
        success: true,
      });

      this.logger.log(`Connection cached for user: ${userContext.userId}. Pool size: ${this.connectionPool.size}`);
      return adapter;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.performanceMonitor.recordMetric({
        operation: 'getConnection',
        duration,
        timestamp: new Date(),
        success: false,
        error: error.message,
      });

      this.logger.error(`Failed to get connection for user: ${userContext.userId}`, error);
      throw error;
    }
  }

  /**
   * Remove a specific user's connection from the pool
   */
  async removeConnection(userContext: UserContext): Promise<void> {
    const cacheKey = this.generateCacheKey(userContext);
    const cachedConnection = this.connectionPool.get(cacheKey);
    
    if (cachedConnection) {
      try {
        await cachedConnection.adapter.disconnect();
        this.connectionPool.delete(cacheKey);
        this.logger.log(`Removed connection for user: ${userContext.userId}`);
      } catch (error) {
        this.logger.error(`Error removing connection for user: ${userContext.userId}`, error);
      }
    }
  }

  /**
   * Remove all connections for a specific user (across all Odoo instances)
   */
  async removeUserConnections(userId: string): Promise<void> {
    const keysToRemove: string[] = [];
    
    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      if (key.startsWith(`${userId}:`)) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      const cachedConnection = this.connectionPool.get(key);
      if (cachedConnection) {
        try {
          await cachedConnection.adapter.disconnect();
          this.connectionPool.delete(key);
        } catch (error) {
          this.logger.error(`Error removing connection for key: ${key}`, error);
        }
      }
    }

    this.logger.log(`Removed ${keysToRemove.length} connections for user: ${userId}`);
  }

  /**
   * Get connection pool statistics
   */
  getPoolStats() {
    return {
      size: this.connectionPool.size,
      maxSize: this.connectionPool.max,
      connections: Array.from(this.connectionPool.entries()).map(([key, cached]) => ({
        key,
        lastUsed: cached.lastUsed,
        host: cached.connectionConfig.host,
        database: cached.connectionConfig.database,
      })),
    };
  }

  /**
   * Warm up connections for frequently used configurations
   */
  async warmUpConnections(configs: Array<{ userId: string; config: OdooConnectionConfig }>): Promise<void> {
    this.logger.log(`Warming up ${configs.length} connections...`);

    const warmupPromises = configs.map(async ({ userId, config }) => {
      try {
        const userContext: UserContext = {
          userId,
          sessionId: `warmup_${Date.now()}`,
          odooConfig: config,
        };

        await this.getConnection(userContext);
        this.logger.debug(`Warmed up connection for user: ${userId}`);
      } catch (error) {
        this.logger.warn(`Failed to warm up connection for user: ${userId}`, error);
      }
    });

    await Promise.allSettled(warmupPromises);
    this.logger.log(`Connection warmup completed. Pool size: ${this.connectionPool.size}`);
  }

  /**
   * Batch operation: Execute multiple operations across different connections
   */
  async executeBatchOperations<T>(
    operations: Array<{
      userContext: UserContext;
      operation: (adapter: UniversalOdooAdapter) => Promise<T>;
    }>,
  ): Promise<Array<{ success: boolean; result?: T; error?: string; userId: string }>> {
    this.logger.log(`Executing ${operations.length} batch operations...`);

    const results = await Promise.allSettled(
      operations.map(async ({ userContext, operation }) => {
        try {
          const adapter = await this.getConnection(userContext);
          const result = await operation(adapter);
          return { success: true, result, userId: userContext.userId };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            userId: userContext.userId
          };
        }
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          success: false,
          error: result.reason?.message || 'Unknown error',
          userId: operations[index].userContext.userId,
        };
      }
    });
  }

  /**
   * Enhanced health check for all connections in pool
   */
  async healthCheck(): Promise<{
    totalConnections: number;
    healthyConnections: number;
    unhealthyConnections: number;
    details: HealthCheckResult[];
    poolMetrics: PoolMetrics;
  }> {
    const startTime = Date.now();
    const connections = Array.from(this.connectionPool.entries());

    const healthChecks = await Promise.allSettled(
      connections.map(async ([key, cached]): Promise<HealthCheckResult> => {
        const checkStartTime = Date.now();
        try {
          const isHealthy = await this.validateConnectionWithTimeout(cached.adapter);
          const responseTime = Date.now() - checkStartTime;

          // Update cached connection health status
          cached.isHealthy = isHealthy;
          cached.lastHealthCheck = new Date();
          if (isHealthy) {
            cached.consecutiveFailures = 0;
          } else {
            cached.consecutiveFailures++;
          }

          return {
            key,
            healthy: isHealthy,
            lastUsed: cached.lastUsed,
            lastHealthCheck: cached.lastHealthCheck,
            consecutiveFailures: cached.consecutiveFailures,
            responseTime,
          };
        } catch (error) {
          cached.isHealthy = false;
          cached.consecutiveFailures++;

          return {
            key,
            healthy: false,
            lastUsed: cached.lastUsed,
            lastHealthCheck: cached.lastHealthCheck,
            consecutiveFailures: cached.consecutiveFailures,
            responseTime: Date.now() - checkStartTime,
            error: error.message,
          };
        }
      })
    );

    const details: HealthCheckResult[] = healthChecks.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        const [key, cached] = connections[index];
        return {
          key,
          healthy: false,
          lastUsed: cached.lastUsed,
          lastHealthCheck: cached.lastHealthCheck,
          consecutiveFailures: cached.consecutiveFailures,
          error: 'Health check promise failed',
        };
      }
    });

    const healthyCount = details.filter(d => d.healthy).length;
    const totalDuration = Date.now() - startTime;

    // Record performance metric
    this.performanceMonitor.recordMetric({
      operation: 'healthCheck',
      duration: totalDuration,
      timestamp: new Date(),
      success: true,
    });

    this.logger.log(`Health check completed in ${totalDuration}ms: ${healthyCount}/${connections.length} healthy`);

    return {
      totalConnections: connections.length,
      healthyConnections: healthyCount,
      unhealthyConnections: connections.length - healthyCount,
      details,
      poolMetrics: this.getPoolMetrics(),
    };
  }

  /**
   * Check if a connection exists for a user
   */
  hasConnection(userContext: UserContext): boolean {
    const cacheKey = this.generateCacheKey(userContext);
    return this.connectionPool.has(cacheKey);
  }

  /**
   * Find existing connection for a user by user ID
   */
  findUserConnection(userId: string): CachedConnection | null {
    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      if (key.startsWith(`${userId}:`)) {
        return cachedConnection;
      }
    }
    return null;
  }

  /**
   * Generate cache key for user connection
   */
  private generateCacheKey(userContext: UserContext): string {
    const { userId, odooConfig } = userContext;
    // Include host, database, and username to handle multiple Odoo instances per user
    return `${userId}:${odooConfig.host}:${odooConfig.database}:${odooConfig.username}`;
  }

  /**
   * Validate if a connection is still active and authenticated
   */
  private async validateConnection(adapter: UniversalOdooAdapter): Promise<boolean> {
    try {
      // Check if adapter has version info (basic connection check)
      const versionInfo = adapter.getVersionInfo();
      if (!versionInfo) {
        this.logger.debug('Connection validation failed: no version info');
        return false;
      }

      // Check if adapter has capabilities (authentication check)
      const capabilities = adapter.getCapabilities();
      if (!capabilities) {
        this.logger.debug('Connection validation failed: no capabilities');
        return false;
      }

      // Try a simple authenticated operation to verify the connection is still valid
      try {
        // This will throw if not authenticated or connection is lost
        await adapter.searchRead('res.users', [['id', '=', 1]], { limit: 1 });
        return true;
      } catch (authError) {
        this.logger.debug('Connection validation failed: authentication test failed', authError.message);
        return false;
      }
    } catch (error) {
      this.logger.debug('Connection validation failed', error);
      return false;
    }
  }

  /**
   * Create a new Odoo adapter connection with retry logic
   */
  private async createConnectionWithRetry(
    config: OdooConnectionConfig,
    maxRetries: number = 3,
  ): Promise<UniversalOdooAdapter> {
    let lastError: Error = new Error('No attempts made');

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const adapter = await this.createConnection(config);
        this.logger.log(`Connection created successfully on attempt ${attempt}`);
        return adapter;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        this.logger.warn(`Connection attempt ${attempt} failed:`, lastError.message);

        if (attempt < maxRetries) {
          // Exponential backoff: 1s, 2s, 4s
          const delay = Math.pow(2, attempt - 1) * 1000;
          this.logger.log(`Retrying in ${delay}ms...`);
          await this.sleep(delay);
        }
      }
    }

    this.logger.error(`Failed to create connection after ${maxRetries} attempts`);
    throw lastError;
  }

  /**
   * Create a new Odoo adapter connection
   */
  private async createConnection(config: OdooConnectionConfig): Promise<UniversalOdooAdapter> {
    const adapter = new UniversalOdooAdapter(
      this.xmlRpcProtocol,
      this.jsonRpcProtocol,
      this.restApiProtocol,
      this.odooV18Adapter,
      this.odooV17Adapter,
      this.odooV15Adapter,
      this.odooV13Adapter,
      this.performanceMonitor,
    );

    try {
      adapter.setConnectionConfig(config);
      await adapter.connect();
      await adapter.authenticate();
      return adapter;
    } catch (error) {
      this.logger.error('Failed to create Odoo connection', error);
      throw error;
    }
  }

  /**
   * Sleep utility for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup stale connections (older than specified age)
   */
  async cleanupStaleConnections(maxAgeMinutes: number = 60): Promise<number> {
    const cutoffTime = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
    const keysToRemove: string[] = [];

    for (const [key, cached] of this.connectionPool.entries()) {
      if (cached.lastUsed < cutoffTime) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      const cached = this.connectionPool.get(key);
      if (cached) {
        try {
          await cached.adapter.disconnect();
          this.connectionPool.delete(key);
        } catch (error) {
          this.logger.error(`Error cleaning up stale connection: ${key}`, error);
        }
      }
    }

    if (keysToRemove.length > 0) {
      this.logger.log(`Cleaned up ${keysToRemove.length} stale connections`);
    }

    return keysToRemove.length;
  }

  /**
   * Preemptively refresh connections that are about to expire
   */
  async refreshExpiringConnections(refreshThresholdMinutes: number = 25): Promise<number> {
    const refreshTime = new Date(Date.now() - refreshThresholdMinutes * 60 * 1000);
    let refreshedCount = 0;

    for (const [key, cached] of this.connectionPool.entries()) {
      if (cached.lastUsed < refreshTime) {
        try {
          // Validate and refresh if needed
          const isValid = await this.validateConnection(cached.adapter);
          if (!isValid) {
            // Recreate connection
            const newAdapter = await this.createConnectionWithRetry(cached.connectionConfig);
            cached.adapter = newAdapter;
            cached.lastUsed = new Date();
            refreshedCount++;
            this.logger.debug(`Refreshed connection for key: ${key}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to refresh connection: ${key}`, error);
          // Remove invalid connection
          this.connectionPool.delete(key);
        }
      }
    }

    if (refreshedCount > 0) {
      this.logger.log(`Refreshed ${refreshedCount} expiring connections`);
    }

    return refreshedCount;
  }

  /**
   * Get enhanced pool metrics for monitoring
   */
  getPoolMetrics(): PoolMetrics {
    const now = new Date();
    const connections = Array.from(this.connectionPool.entries());

    const ages = connections.map(([, cached]) =>
      now.getTime() - cached.lastUsed.getTime()
    );

    const usageCounts = connections.map(([, cached]) => cached.usageCount);
    const healthyConnections = connections.filter(([, cached]) => cached.isHealthy).length;

    return {
      size: this.connectionPool.size,
      maxSize: this.connectionPool.max,
      utilizationPercent: (this.connectionPool.size / this.connectionPool.max) * 100,
      averageAgeMinutes: ages.length > 0 ? ages.reduce((a, b) => a + b, 0) / ages.length / 60000 : 0,
      oldestConnectionMinutes: ages.length > 0 ? Math.max(...ages) / 60000 : 0,
      newestConnectionMinutes: ages.length > 0 ? Math.min(...ages) / 60000 : 0,
      totalUsageCount: usageCounts.reduce((a, b) => a + b, 0),
      averageUsageCount: usageCounts.length > 0 ? usageCounts.reduce((a, b) => a + b, 0) / usageCounts.length : 0,
      healthyConnections,
      unhealthyConnections: connections.length - healthyConnections,
      lastCleanupTime: this.lastCleanupTime,
      lastHealthCheckTime: this.lastHealthCheckTime,
    };
  }

  /**
   * Cleanup all connections on module destroy
   */
  async onModuleDestroy() {
    this.logger.log('Cleaning up all Odoo connections...');

    // Stop background timers
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    const disconnectPromises: Promise<void>[] = [];

    for (const [key, cachedConnection] of this.connectionPool.entries()) {
      disconnectPromises.push(
        this.disposeConnection(cachedConnection, key).catch((error) => {
          this.logger.error(`Error disconnecting adapter for key: ${key}`, error);
        })
      );
    }

    await Promise.allSettled(disconnectPromises);
    this.connectionPool.clear();

    this.logger.log('All Odoo connections cleaned up');
  }

  // ========== PRIVATE HELPER METHODS ==========

  /**
   * Check if a connection needs a health check
   */
  private needsHealthCheck(connection: CachedConnection): boolean {
    const now = new Date();
    const timeSinceLastCheck = now.getTime() - connection.lastHealthCheck.getTime();
    const healthCheckInterval = this.poolConfig.healthCheckIntervalMinutes * 60 * 1000;

    return timeSinceLastCheck > healthCheckInterval || !connection.isHealthy;
  }

  /**
   * Validate connection with timeout
   */
  private async validateConnectionWithTimeout(adapter: UniversalOdooAdapter): Promise<boolean> {
    try {
      return await Promise.race([
        this.validateConnection(adapter),
        new Promise<boolean>((_, reject) =>
          setTimeout(() => reject(new Error('Health check timeout')), 5000)
        )
      ]);
    } catch (error) {
      this.logger.debug('Connection validation failed with timeout', error);
      return false;
    }
  }

  /**
   * Enhanced connection disposal with proper cleanup
   */
  private async disposeConnection(cachedConnection: CachedConnection, key: string): Promise<void> {
    try {
      await cachedConnection.adapter.disconnect();
      this.logger.debug(`Disposed connection for key: ${key}`);
    } catch (error) {
      this.logger.warn(`Error disposing connection for key: ${key}`, error);
    }
  }

  /**
   * Start background health check timer
   */
  private startHealthCheckTimer(): void {
    const intervalMs = this.poolConfig.healthCheckIntervalMinutes * 60 * 1000;

    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.performBackgroundHealthCheck();
      } catch (error) {
        this.logger.error('Background health check failed', error);
      }
    }, intervalMs);

    this.logger.log(`Background health checks started (interval: ${this.poolConfig.healthCheckIntervalMinutes}min)`);
  }

  /**
   * Start background cleanup timer
   */
  private startCleanupTimer(): void {
    const intervalMs = this.poolConfig.cleanupIntervalMinutes * 60 * 1000;

    this.cleanupTimer = setInterval(async () => {
      try {
        await this.performBackgroundCleanup();
      } catch (error) {
        this.logger.error('Background cleanup failed', error);
      }
    }, intervalMs);

    this.logger.log(`Background cleanup started (interval: ${this.poolConfig.cleanupIntervalMinutes}min)`);
  }

  /**
   * Perform background health check on all connections
   */
  private async performBackgroundHealthCheck(): Promise<void> {
    const startTime = Date.now();
    const connections = Array.from(this.connectionPool.entries());

    if (connections.length === 0) {
      return;
    }

    this.logger.debug(`Starting background health check for ${connections.length} connections`);

    let healthyCount = 0;
    let unhealthyCount = 0;

    const healthCheckPromises = connections.map(async ([key, cached]) => {
      try {
        const isHealthy = await this.validateConnectionWithTimeout(cached.adapter);
        cached.isHealthy = isHealthy;
        cached.lastHealthCheck = new Date();

        if (isHealthy) {
          cached.consecutiveFailures = 0;
          healthyCount++;
        } else {
          cached.consecutiveFailures++;
          unhealthyCount++;

          // Remove connections with too many failures
          if (cached.consecutiveFailures >= this.poolConfig.maxConsecutiveFailures) {
            this.logger.warn(`Removing unhealthy connection: ${key} (${cached.consecutiveFailures} failures)`);
            this.connectionPool.delete(key);
          }
        }
      } catch (error) {
        this.logger.warn(`Health check failed for connection: ${key}`, error);
        cached.isHealthy = false;
        cached.consecutiveFailures++;
        unhealthyCount++;
      }
    });

    await Promise.allSettled(healthCheckPromises);

    const duration = Date.now() - startTime;
    this.lastHealthCheckTime = new Date();

    this.logger.log(
      `Background health check completed in ${duration}ms: ${healthyCount} healthy, ${unhealthyCount} unhealthy`
    );
  }

  /**
   * Perform background cleanup of stale connections
   */
  private async performBackgroundCleanup(): Promise<void> {
    const startTime = Date.now();
    const cleanedCount = await this.cleanupStaleConnections();
    const duration = Date.now() - startTime;

    this.lastCleanupTime = new Date();

    if (cleanedCount > 0) {
      this.logger.log(`Background cleanup completed in ${duration}ms: removed ${cleanedCount} stale connections`);
    } else {
      this.logger.debug(`Background cleanup completed in ${duration}ms: no stale connections found`);
    }
  }
}
